import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { Package, Users, FileText, DollarSign, TrendingUp, RefreshCw, Trash } from 'lucide-react'
import ModernButton from '../components/ModernButton'
import CreateInvoiceButton from '../components/CreateInvoiceButton'
import { useGlobalDialog } from '../contexts/DialogContext'
import { getProducts, getCustomers, getInvoices, initializeSampleData, resetToDefaultData, syncDefaultData } from '../services/storage'
import { migrateAllToSupabase } from '../utils/migrateToSupabase'

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalProducts: 0,
    totalCustomers: 0,
    totalInvoices: 0,
    totalRevenue: 0,
    recentInvoices: []
  })
  const [currentTime, setCurrentTime] = useState(new Date())
  const { confirm, alert, success, info } = useGlobalDialog()

  useEffect(() => {
    async function loadData() {
      // Remove initializeSampleData() if you want to use only Supabase
      // await initializeSampleData();
      const products = await getProducts();
      const customers = await getCustomers();
      const invoices = await getInvoices();

      // Calculate stats
      const totalRevenueRaw = Array.isArray(invoices) ? invoices.reduce((sum, invoice) => sum + (invoice.total || 0), 0) : 0;
      // Get total pengeluaran (expenses) from localStorage (or migrate to Supabase if needed)
      let totalPengeluaran = 0;
      try {
        const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
        totalPengeluaran = expenses.reduce((sum, exp) => sum + (Number(exp.amount) || 0), 0);
      } catch (e) { totalPengeluaran = 0; }
      const totalRevenue = totalRevenueRaw - totalPengeluaran;
      const recentInvoices = Array.isArray(invoices)
        ? invoices
            .sort((a, b) => new Date(b.createdat) - new Date(a.createdat))
            .slice(0, 5)
            .map(invoice => {
              const customer = customers.find(c => c.id === invoice.pelangganId)
              return {
                ...invoice,
                customerName: customer ? customer.nama : 'Pelanggan Tidak Ditemukan'
              }
            })
        : [];

      setStats({
        totalProducts: Array.isArray(products) ? products.length : 0,
        totalCustomers: Array.isArray(customers) ? customers.length : 0,
        totalInvoices: Array.isArray(invoices) ? invoices.length : 0,
        totalRevenue,
        recentInvoices
      });
    }
    loadData();
  }, [])

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000)
    return () => clearInterval(timer)
  }, [])

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR'
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('id-ID')
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'sent':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const handleResetData = async () => {
    const confirmed = await confirm({
      title: 'Reset Data',
      message: 'Apakah Anda yakin ingin mereset semua data ke data default?\n\nSemua data yang ada akan hilang dan tidak dapat dikembalikan.',
      confirmText: 'Reset',
      cancelText: 'Batal',
      confirmButtonStyle: 'danger'
    })

    if (confirmed) {
      try {
        resetToDefaultData()
        await success({
          title: 'Berhasil!',
          message: 'Data berhasil direset ke data default.'
        })
        window.location.reload() // Refresh page to show new data
      } catch (error) {
        await alert({
          title: 'Error!',
          message: 'Terjadi kesalahan saat mereset data.'
        })
      }
    }
  }

  const handleSyncData = async () => {
    const confirmed = await confirm({
      title: 'Sinkronisasi Data',
      message: 'Sinkronisasi akan menambahkan produk dan pelanggan default terbaru tanpa menghapus data yang sudah ada.\n\nLanjutkan?',
      confirmText: 'Sinkronisasi',
      cancelText: 'Batal'
    })

    if (confirmed) {
      try {
        const result = syncDefaultData()
        if (result.updated) {
          await success({
            title: 'Berhasil!',
            message: result.message
          })
          window.location.reload() // Refresh to show new data
        } else {
          await info({
            title: 'Informasi',
            message: 'Data sudah up to date!'
          })
        }
      } catch (error) {
        await alert({
          title: 'Error!',
          message: 'Terjadi kesalahan saat sinkronisasi data.'
        })
      }
    }
  }

  const handleMigrateData = async () => {
    const result = await migrateAllToSupabase();
    await success({
      title: 'Migrasi Selesai',
      message: `Migrasi data selesai!\n\n${JSON.stringify(result, null, 2)}`
    });
    window.location.reload();
  };

  const statCards = [
    {
      title: 'Total Produk',
      value: stats.totalProducts,
      icon: Package,
      color: 'bg-blue-500',
      link: '/products'
    },
    {
      title: 'Total Pelanggan',
      value: stats.totalCustomers,
      icon: Users,
      color: 'bg-green-500',
      link: '/customers'
    },
    {
      title: 'Total Invoice',
      value: stats.totalInvoices,
      icon: FileText,
      color: 'bg-purple-500',
      link: '/invoices'
    },
    {
      title: 'Total Pendapatan',
      value: formatCurrency(stats.totalRevenue),
      icon: DollarSign,
      color: 'bg-yellow-500',
      link: '/invoices'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Digital Clock */}
      <div className="text-2xl font-mono text-center mb-4">
        {currentTime.toLocaleTimeString('id-ID', { hour12: false })}
      </div>

      {/* Welcome Section */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-900">Selamat Datang di Roti Ragil</h1>
            <p className="text-gray-600 mt-1">Kelola invoice dan bisnis Anda dengan mudah</p>
          </div>
          <div className="flex items-center space-x-3">
            <ModernButton
              onClick={handleSyncData}
              startIcon={<RefreshCw style={{ fontSize: 18, marginRight: 4 }} />}
              color="primary"
              size="small"
              style={{ fontSize: 14, padding: '6px 16px', minHeight: 32 }}
            >
              Sync Data
            </ModernButton>
            <ModernButton
              onClick={handleResetData}
              startIcon={<Trash style={{ fontSize: 18, marginRight: 4 }} />}
              color="error"
              size="small"
              style={{ fontSize: 14, padding: '6px 16px', minHeight: 32 }}
            >
              Reset Data
            </ModernButton>
            <ModernButton
              onClick={handleMigrateData}
              startIcon={<RefreshCw style={{ fontSize: 18, marginRight: 4 }} />}
              color="secondary"
              size="small"
              style={{ fontSize: 14, padding: '6px 16px', minHeight: 32 }}
            >
              Migrasi Data ke Supabase
            </ModernButton>
            <div className="hidden sm:block">
              <TrendingUp className="h-12 w-12 text-blue-500" />
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((card, index) => {
          const Icon = card.icon
          return (
            <Link
              key={index}
              to={card.link}
              className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow"
            >
              <div className="flex items-center">
                <div className={`${card.color} rounded-lg p-3`}>
                  <Icon className="h-6 w-6 text-white" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{card.title}</p>
                  <p
  className="font-semibold text-gray-900"
  style={{
    fontSize: 'clamp(1.25rem, 2.5vw, 2rem)',
    maxWidth: 160,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    lineHeight: 1.2
  }}
  title={card.value}
>
  {card.value}
</p>
                </div>
              </div>
            </Link>
          )
        })}
      </div>

      {/* Recent Invoices */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-medium text-gray-900">Invoice Terbaru</h2>
            <Link
              to="/invoices"
              className="text-sm text-blue-600 hover:text-blue-500"
            >
              Lihat Semua
            </Link>
          </div>
        </div>
        <div className="divide-y divide-gray-200">
          {stats.recentInvoices.length > 0 ? (
            stats.recentInvoices.map((invoice) => (
              <div key={invoice.id} className="px-6 py-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {invoice.nomorInvoice}
                    </p>
                    <p className="text-sm text-gray-600">
                      {invoice.customerName}
                    </p>
                    <p className="text-sm text-gray-500">
                      {formatDate(invoice.createdat)}
                    </p>
                  </div>
                  <div className="flex items-center space-x-4">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(invoice.status)}`}>
                      {invoice.status === 'paid' ? 'Lunas' :
                       invoice.status === 'sent' ? 'Terkirim' : 'Draft'}
                    </span>
                    <p className="text-sm font-medium text-gray-900">
                      {formatCurrency(invoice.total || 0)}
                    </p>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="px-6 py-8 text-center">
              <FileText className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Belum ada invoice</h3>
              <p className="mt-1 text-sm text-gray-500">
                Mulai dengan membuat invoice pertama Anda.
              </p>
              <div className="mt-6">
                <CreateInvoiceButton
                  variant="primary"
                  size="default"
                  iconType="zap"
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default Dashboard
