import { getInvoices, getCustomers } from './storage'
import { startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, startOfYear, endOfYear, format, parseISO, isWithinInterval, subDays, subWeeks, subMonths, subYears } from 'date-fns'
import { id } from 'date-fns/locale'

// Get revenue data by period
export const getRevenueByPeriod = async (period = 'month', startDate = null, endDate = null) => {
  const invoicesRaw = await getInvoices()
  const invoices = Array.isArray(invoicesRaw) ? invoicesRaw.filter(invoice => invoice.status === 'paid') : []
  const customersRaw = await getCustomers()
  const customers = Array.isArray(customersRaw) ? customersRaw : []

  let filteredInvoices = invoices

  if (startDate && endDate) {
    // Custom date range
    const start = startOfDay(parseISO(startDate))
    const end = endOfDay(parseISO(endDate))

    filteredInvoices = invoices.filter(invoice => {
      // Use paidAt date if available, otherwise fall back to invoice date
      const dateToUse = invoice.paidAt || invoice.tanggal
      const invoiceDate = parseISO(dateToUse)
      return isWithinInterval(invoiceDate, { start, end })
    })
  } else {
    // Predefined periods
    const now = new Date()
    let start, end

    switch (period) {
      case 'day':
        start = startOfDay(now)
        end = endOfDay(now)
        break
      case 'week':
        start = startOfWeek(now, { locale: id })
        end = endOfWeek(now, { locale: id })
        break
      case 'month':
        start = startOfMonth(now)
        end = endOfMonth(now)
        break
      case 'year':
        start = startOfYear(now)
        end = endOfYear(now)
        break
      default:
        start = startOfMonth(now)
        end = endOfMonth(now)
    }

    filteredInvoices = invoices.filter(invoice => {
      // Use paidAt date if available, otherwise fall back to invoice date
      const dateToUse = invoice.paidAt || invoice.tanggal
      const invoiceDate = parseISO(dateToUse)
      return isWithinInterval(invoiceDate, { start, end })
    })
  }
  
  // Calculate total revenue
  const totalRevenue = filteredInvoices.reduce((sum, invoice) => sum + invoice.total, 0)
  
  // Calculate average per invoice
  const averagePerInvoice = filteredInvoices.length > 0 ? totalRevenue / filteredInvoices.length : 0
  
  // Get customer names
  const invoicesWithCustomers = filteredInvoices.map(invoice => ({
    ...invoice,
    customerName: customers.find(c => c.id === invoice.pelangganId)?.nama || 'Unknown'
  }))
  
  return {
    totalRevenue,
    totalInvoices: filteredInvoices.length,
    averagePerInvoice,
    invoices: invoicesWithCustomers,
    period
  }
}

// Get revenue trend data for charts
export const getRevenueTrend = async (period = 'month', count = 12) => {
  const invoicesRaw = await getInvoices()
  const invoices = Array.isArray(invoicesRaw) ? invoicesRaw.filter(invoice => invoice.status === 'paid') : []
  const now = new Date()
  const data = []
  
  for (let i = count - 1; i >= 0; i--) {
    let start, end, label
    
    switch (period) {
      case 'day':
        const day = subDays(now, i)
        start = startOfDay(day)
        end = endOfDay(day)
        label = format(day, 'dd MMM', { locale: id })
        break
      case 'week':
        const week = subWeeks(now, i)
        start = startOfWeek(week, { locale: id })
        end = endOfWeek(week, { locale: id })
        label = `Minggu ${format(start, 'dd MMM', { locale: id })}`
        break
      case 'month':
        const month = subMonths(now, i)
        start = startOfMonth(month)
        end = endOfMonth(month)
        label = format(month, 'MMM yyyy', { locale: id })
        break
      case 'year':
        const year = subYears(now, i)
        start = startOfYear(year)
        end = endOfYear(year)
        label = format(year, 'yyyy')
        break
      default:
        const defaultMonth = subMonths(now, i)
        start = startOfMonth(defaultMonth)
        end = endOfMonth(defaultMonth)
        label = format(defaultMonth, 'MMM yyyy', { locale: id })
    }
    
    const periodInvoices = invoices.filter(invoice => {
      const dateToUse = invoice.paidAt || invoice.tanggal
      const invoiceDate = parseISO(dateToUse)
      return isWithinInterval(invoiceDate, { start, end })
    })
    
    const revenue = periodInvoices.reduce((sum, invoice) => sum + invoice.total, 0)
    
    data.push({
      period: label,
      revenue,
      invoiceCount: periodInvoices.length,
      date: format(start, 'yyyy-MM-dd')
    })
  }
  
  return data
}

// Get top customers by revenue
export const getTopCustomers = async (period = 'month', limit = 5) => {
  const report = await getRevenueByPeriod(period)
  const invoices = Array.isArray(report.invoices) ? report.invoices : []
  const customersRaw = await getCustomers()
  const customers = Array.isArray(customersRaw) ? customersRaw : []
  
  // Group by customer
  const customerRevenue = {}
  
  invoices.forEach(invoice => {
    const customerId = invoice.pelangganId
    if (!customerRevenue[customerId]) {
      customerRevenue[customerId] = {
        customerId,
        customerName: customers.find(c => c.id === customerId)?.nama || 'Unknown',
        totalRevenue: 0,
        invoiceCount: 0
      }
    }
    customerRevenue[customerId].totalRevenue += invoice.total
    customerRevenue[customerId].invoiceCount++
  })
  
  // Sort by revenue and limit
  const sorted = Object.values(customerRevenue).sort((a, b) => b.totalRevenue - a.totalRevenue)
  return sorted.slice(0, limit)
}

// Get revenue summary statistics
export const getRevenueSummary = async () => {
  const invoicesRaw = await getInvoices()
  const invoices = Array.isArray(invoicesRaw) ? invoicesRaw.filter(invoice => invoice.status === 'paid') : []
  const now = new Date()

  // Today
  const todayRevenue = (await getRevenueByPeriod('day')).totalRevenue

  // This week
  const weekRevenue = (await getRevenueByPeriod('week')).totalRevenue

  // This month
  const monthRevenue = (await getRevenueByPeriod('month')).totalRevenue

  // This year
  const yearRevenue = (await getRevenueByPeriod('year')).totalRevenue

  // Previous month for comparison
  const lastMonth = subMonths(now, 1)
  const lastMonthStart = format(startOfMonth(lastMonth), 'yyyy-MM-dd')
  const lastMonthEnd = format(endOfMonth(lastMonth), 'yyyy-MM-dd')
  const lastMonthRevenue = (await getRevenueByPeriod('month', lastMonthStart, lastMonthEnd)).totalRevenue

  // Calculate growth
  const monthGrowth = lastMonthRevenue > 0 
    ? ((monthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 
    : 0

  return {
    today: todayRevenue,
    week: weekRevenue,
    month: monthRevenue,
    year: yearRevenue,
    lastMonth: lastMonthRevenue,
    monthGrowth: monthGrowth
  }
}

// --- EXPENSE AGGREGATION FUNCTIONS ---

// Get expense (pengeluaran) data by period
export const getExpenseByPeriod = async (period = 'month', startDate = null, endDate = null) => {
  // Get expenses from localStorage (legacy) and Supabase
  let expenses = [];

  // Try to get from localStorage first (for backward compatibility)
  try {
    const localExpenses = JSON.parse(localStorage.getItem('expenses') || '[]');
    expenses = Array.isArray(localExpenses) ? localExpenses : [];
  } catch (error) {
    console.warn('Error reading expenses from localStorage:', error);
  }

  // TODO: Add Supabase expenses when implemented
  // const { data: supabaseExpenses } = await supabase.from('expenses').select('*');
  // if (supabaseExpenses) expenses = [...expenses, ...supabaseExpenses];

  let filteredRecords = expenses;

  if (startDate && endDate) {
    const start = startOfDay(parseISO(startDate));
    const end = endOfDay(parseISO(endDate));
    filteredRecords = expenses.filter(expense => {
      const expenseDate = expense.date;
      if (!expenseDate) return false;
      try {
        // Parse YYYY-MM-DD format
        const recordDate = parseISO(expenseDate);
        return isWithinInterval(recordDate, { start, end });
      } catch (error) {
        console.warn('Invalid date format in expense:', expenseDate);
        return false;
      }
    });
  } else {
    const now = new Date();
    let start, end;
    switch (period) {
      case 'day':
        start = startOfDay(now); end = endOfDay(now); break;
      case 'week':
        start = startOfWeek(now, { locale: id }); end = endOfWeek(now, { locale: id }); break;
      case 'month':
        start = startOfMonth(now); end = endOfMonth(now); break;
      case 'year':
        start = startOfYear(now); end = endOfYear(now); break;
      default:
        start = startOfMonth(now); end = endOfMonth(now);
    }
    filteredRecords = expenses.filter(expense => {
      const expenseDate = expense.date;
      if (!expenseDate) return false;
      try {
        // Parse YYYY-MM-DD format
        const recordDate = parseISO(expenseDate);
        return isWithinInterval(recordDate, { start, end });
      } catch (error) {
        console.warn('Invalid date format in expense:', expenseDate);
        return false;
      }
    });
  }

  // Calculate total expenses (sum of all expense amounts)
  const totalExpense = filteredRecords.reduce((sum, expense) => {
    return sum + (Number(expense.amount) || 0);
  }, 0);

  // Average per record
  const averagePerRecord = filteredRecords.length > 0 ? totalExpense / filteredRecords.length : 0;

  return {
    totalExpense,
    totalRecords: filteredRecords.length,
    averagePerRecord,
    records: filteredRecords,
    period
  };
};

// Get expense trend data for charts
export const getExpenseTrend = async (period = 'month', count = 12) => {
  // Get expenses from localStorage (legacy)
  let expenses = [];
  try {
    const localExpenses = JSON.parse(localStorage.getItem('expenses') || '[]');
    expenses = Array.isArray(localExpenses) ? localExpenses : [];
  } catch (error) {
    console.warn('Error reading expenses from localStorage:', error);
  }

  const now = new Date();
  const data = [];

  for (let i = count - 1; i >= 0; i--) {
    let start, end, label;
    switch (period) {
      case 'day': {
        const day = subDays(now, i);
        start = startOfDay(day); end = endOfDay(day);
        label = format(day, 'dd MMM', { locale: id });
        break;
      }
      case 'week': {
        const week = subWeeks(now, i);
        start = startOfWeek(week, { locale: id }); end = endOfWeek(week, { locale: id });
        label = `Minggu ${format(start, 'dd MMM', { locale: id })}`;
        break;
      }
      case 'month': {
        const month = subMonths(now, i);
        start = startOfMonth(month); end = endOfMonth(month);
        label = format(month, 'MMM yyyy', { locale: id });
        break;
      }
      case 'year': {
        const year = subYears(now, i);
        start = startOfYear(year); end = endOfYear(year);
        label = format(year, 'yyyy');
        break;
      }
      default: {
        const defaultMonth = subMonths(now, i);
        start = startOfMonth(defaultMonth); end = endOfMonth(defaultMonth);
        label = format(defaultMonth, 'MMM yyyy', { locale: id });
      }
    }
    const periodExpenses = expenses.filter(expense => {
      const expenseDate = expense.date;
      if (!expenseDate) return false;
      try {
        // Parse YYYY-MM-DD format
        const recordDate = parseISO(expenseDate);
        return isWithinInterval(recordDate, { start, end });
      } catch (error) {
        console.warn('Invalid date format in expense:', expenseDate);
        return false;
      }
    });
    const expense = periodExpenses.reduce((sum, expenseRecord) => {
      return sum + (Number(expenseRecord.amount) || 0);
    }, 0);
    data.push({
      period: label,
      expense,
      recordCount: periodRecords.length,
      date: format(start, 'yyyy-MM-dd')
    });
  }
  return data;
};

// Get expense summary statistics
export const getExpenseSummary = async () => {
  const now = new Date();
  // Today
  const todayData = await getExpenseByPeriod('day');
  const todayExpense = todayData.totalExpense;
  // This week
  const weekData = await getExpenseByPeriod('week');
  const weekExpense = weekData.totalExpense;
  // This month
  const monthData = await getExpenseByPeriod('month');
  const monthExpense = monthData.totalExpense;
  // This year
  const yearData = await getExpenseByPeriod('year');
  const yearExpense = yearData.totalExpense;
  // Previous month for comparison
  const lastMonth = subMonths(now, 1);
  const lastMonthStart = format(startOfMonth(lastMonth), 'yyyy-MM-dd');
  const lastMonthEnd = format(endOfMonth(lastMonth), 'yyyy-MM-dd');
  const lastMonthData = await getExpenseByPeriod('month', lastMonthStart, lastMonthEnd);
  const lastMonthExpense = lastMonthData.totalExpense;
  // Calculate growth
  const monthGrowth = lastMonthExpense > 0 
    ? ((monthExpense - lastMonthExpense) / lastMonthExpense) * 100 
    : 0;
  return {
    today: todayExpense,
    week: weekExpense,
    month: monthExpense,
    year: yearExpense,
    lastMonth: lastMonthExpense,
    monthGrowth: monthGrowth
  };
};

// Export data for external use
export const exportRevenueData = async (period = 'month') => {
  const data = await getRevenueByPeriod(period)
  const trend = await getRevenueTrend(period)
  const topCustomers = await getTopCustomers(period)

  return {
    summary: data,
    trend,
    topCustomers,
    exportDate: format(new Date(), 'yyyy-MM-dd HH:mm:ss', { locale: id })
  }
}
